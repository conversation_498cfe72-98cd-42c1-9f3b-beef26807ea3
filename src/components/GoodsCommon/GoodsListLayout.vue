<template>
  <div class="goods-list-layout">
    <GoodsSkeletonLoader
      v-if="isLoading"
      :is-waterfall="isWaterfall"
      :skeleton-count="isWaterfall ? 4 : 3"
    />

    <section v-show="goodsList.length > 0 && !isWaterfall" class="list-layout">
      <van-list
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoadMore"
        @update:loading="handleUpdateLoading"
      >
        <ul class="goods-list-container">
          <ProductListItem
            v-for="(item, index) in goodsList"
            :key="`goods-${item.skuId || index}`"
            :item="item"
            @item-click="handleItemClick"
            @add-cart="handleAddCart"
          />
        </ul>
      </van-list>
    </section>

    <section v-show="goodsList.length > 0 && isWaterfall" class="waterfall-layout">
      <van-list
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoadMore"
        @update:loading="handleUpdateLoading"
      >
        <Waterfall :list="goodsList" :breakpoints="breakpoints" :hasAroundGutter="false"
          :animation="false" :animationDuration="0" :horizontalOrder="true" :animationDelay="0">
          <template #default="{ item }">
            <GoodsWaterfallItem
              :item="item"
              @item-click="handleItemClick"
              @add-cart="handleAddCart"
            />
          </template>
        </Waterfall>
      </van-list>
    </section>

    <section v-if="goodsList.length <= 0 && !isLoading" class="empty-state">
      <WoEmpty :description="emptyDescription" />
    </section>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import WoEmpty from '@/components/WoElementCom/WoEmpty.vue'
import GoodsSkeletonLoader from './GoodsSkeletonLoader.vue'
import ProductListItem from './ProductListItem.vue'
import GoodsWaterfallItem from './GoodsWaterfallItem.vue'

const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  finished: {
    type: Boolean,
    default: false
  },
  isWaterfall: {
    type: Boolean,
    default: false
  },
  breakpoints: {
    type: Object,
    default: () => ({
      // 超大屏设备 (大平板横屏、桌面)
      1024: { rowPerView: 5 },

      // 大平板设备 (iPad Pro 12.9", iPad Air 横屏)
      834: { rowPerView: 4 },

      // 标准平板设备 (iPad 竖屏, 小平板横屏)
      768: { rowPerView: 4 },

      // 大屏手机横屏 & 小平板竖屏
      667: { rowPerView: 3 },

      // 大屏手机 (iPhone 14 Pro Max, iPhone 13 Pro Max 等)
      430: { rowPerView: 3 },

      // 标准大屏手机 (iPhone 14 Pro, iPhone 13 Pro, 大部分安卓旗舰)
      414: { rowPerView: 2 },

      // 中等屏幕手机 (iPhone 12/13 mini, 中端安卓)
      390: { rowPerView: 2 },

      // 标准屏幕手机 (iPhone SE 3rd, 大部分安卓中端机)
      375: { rowPerView: 2 },

      // 小屏手机 (老款 iPhone, 小屏安卓)
      360: { rowPerView: 2 },

      // 超小屏手机 (iPhone SE 1st/2nd, 老款安卓)
      320: { rowPerView: 2 },

      // 极小屏设备兜底 (折叠屏折叠状态等)
      0: { rowPerView: 1 }
    })
  },
  emptyDescription: {
    type: String,
    default: '暂无商品'
  }
})

const {
  goodsList,
  isLoading,
  loading,
  finished,
  isWaterfall,
  breakpoints,
  emptyDescription
} = toRefs(props)

const emit = defineEmits(['load-more', 'item-click', 'add-cart', 'update:loading'])

const handleLoadMore = () => {
  emit('load-more')
}

const handleUpdateLoading = (value) => {
  emit('update:loading', value)
}

const handleItemClick = (item) => {
  emit('item-click', item)
}

const handleAddCart = (item) => {
  emit('add-cart', item)
}
</script>

<style scoped lang="less">
.goods-list-layout {
  .list-layout {
    margin-top: 2px;

    .goods-list-container {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .empty-state {
    padding: 40px 0;
  }
}
</style>
