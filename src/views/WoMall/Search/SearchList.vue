<template>
  <div class="search-list-page">
    <header class="page-header">
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" @search="handleSearch">
        <template #right-action>
          <button class="layout-toggle" @click="toggleLayout" type="button">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>

      <SortFilterBar
        :sort-type="sortType"
        :sort-order="sortOrder"
        :has-filter-conditions="hasFilterConditions"
        @sort-change="handleSortChange"
        @filter-toggle="toggleFilter"
      />
    </header>

    <main class="goods-content">
      <GoodsListLayout
        :goods-list="goodsList"
        :is-loading="isLoading"
        :loading="loading"
        :finished="finished"
        :is-waterfall="isWaterfallLayout"
        :breakpoints="breakpoints"
        empty-description="未搜到相关商品"
        @load-more="onLoad"
        @item-click="goToDetail"
        @add-cart="addOneCart"
        @update:loading="(val) => loading = val"
      />
    </main>

    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart" />

    <FilterPopup
      v-model:show="isPopupShow"
      v-model="filterCriteria"
      :location-text="locationText"
      :category-id="categoryId"
      :keyword="searchKeyword"
      @switch-address="setSwitchAddressPopupShow"
      @confirm="handleFilterConfirm"
      @reset="handleFilterReset"
    />

    <AddressSwitchPopup
      v-model:show="isSwitchAddressPopupShow"
      @address-changed="handleAddressChanged"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce, compact, get } from 'lodash-es'
import SearchHeader from '@components/Common/SearchHeader.vue'
import AddressSwitchPopup from '@/components/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@/components/FilterTools/FilterPopup.vue'
import FloatingBubble from '@/components/FilterTools/FloatingBubble.vue'
import SortFilterBar from '@/components/FilterTools/SortFilterBar.vue'
import GoodsListLayout from '@/components/GoodsCommon/GoodsListLayout.vue'
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { searchKeyWord } from '@/api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'
import { useUserStore } from '@/store/modules/user.js'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

// 使用商品列表组合函数
const {
  goodsList,
  loading,
  finished,
  isLoading,
  pageNo,
  pageSize,
  filterCriteria,
  hasFilterConditions,
  locationText,
  addressInfo,
  resetList,
  processGoodsData,
  applyStockFilter,
  goToDetail,
  goToCart,
  addOneCart,
  handleFilterReset,
  handleAddressChanged
} = useGoodsList()

// 页面特有状态
const floatingBubbleOffset = ref({ bottom: 150 })
const searchKeyword = ref('')
const sortType = ref('sort')
const sortOrder = ref('desc')
const isWaterfallLayout = ref(false)
const categoryId = ref('')
const isPopupShow = ref(false)
const isSwitchAddressPopupShow = ref(false)

// 瀑布流配置
const breakpoints = ref({
  // 超大屏设备 (大平板横屏、桌面)
  1024: { rowPerView: 5 },

  // 大平板设备 (iPad Pro 12.9", iPad Air 横屏)
  834: { rowPerView: 4 },

  // 标准平板设备 (iPad 竖屏, 小平板横屏)
  768: { rowPerView: 4 },

  // 大屏手机横屏 & 小平板竖屏
  667: { rowPerView: 3 },

  // 大屏手机 (iPhone 14 Pro Max, iPhone 13 Pro Max 等)
  430: { rowPerView: 3 },

  // 标准大屏手机 (iPhone 14 Pro, iPhone 13 Pro, 大部分安卓旗舰)
  414: { rowPerView: 2 },

  // 中等屏幕手机 (iPhone 12/13 mini, 中端安卓)
  390: { rowPerView: 2 },

  // 标准屏幕手机 (iPhone SE 3rd, 大部分安卓中端机)
  375: { rowPerView: 2 },

  // 小屏手机 (老款 iPhone, 小屏安卓)
  360: { rowPerView: 2 },

  // 超小屏手机 (iPhone SE 1st/2nd, 老款安卓)
  320: { rowPerView: 2 },

  // 极小屏设备兜底 (折叠屏折叠状态等)
  0: { rowPerView: 1 }
})

// 页面方法
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

const setSwitchAddressPopupShow = () => {
  isSwitchAddressPopupShow.value = true
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  resetList()
  fetchGoodsList()
}, 300)

const handleSearch = () => {
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      keyword: searchKeyword.value
    }
  })
  debouncedSearch()
}

const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  if (currentSortType === type) {
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    sortType.value = type
    sortOrder.value = ''
  }

  resetList()
  fetchGoodsList()
}

const handleFilterConfirm = () => {
  resetList()
  fetchGoodsList()
}

// 搜索相关状态
const consecutiveEmptyPages = ref(0)
const maxEmptyPages = 2

// 获取商品列表
const fetchGoodsList = async () => {
  showLoadingToast()

  if (pageNo.value === 1) {
    await userStore.queryDefaultAddr({ force: true })
    isLoading.value = true
    goodsList.value = []
    consecutiveEmptyPages.value = 0
    finished.value = false
  }

  const brandList = compact(
    filterCriteria.value.brandsList
      .filter(item => item.isSelected)
      .map(item => item.value)
  )

  const testDMX = get(route.query, 'testDMX', 'false')
  const [err, json] = await searchKeyWord({
    keyword: searchKeyword.value,
    bizCode: getBizCode('GOODS'),
    pageNumber: pageNo.value,
    pageSize: pageSize.value,
    orderType: '00',
    orderRule: sortOrder.value,
    brands: brandList,
    minPrice: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice) : '',
    maxPrice: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice) : '',
    addressJsonInfo: addressInfo.value,
    testDMX
  })

  closeToast()
  loading.value = false
  isLoading.value = false

  if (!err) {
    if (json && json.length > 0) {
      const processedList = processGoodsData(json)
      const filteredList = applyStockFilter(processedList)

      if (filteredList.length > 0) {
        consecutiveEmptyPages.value = 0
        goodsList.value = goodsList.value.concat(filteredList)
        pageNo.value++
      } else {
        consecutiveEmptyPages.value++
        if (consecutiveEmptyPages.value >= maxEmptyPages) {
          finished.value = true
          return
        }
        pageNo.value++
        nextTick(() => onLoad())
      }
    } else {
      consecutiveEmptyPages.value++
      if (consecutiveEmptyPages.value >= maxEmptyPages) {
        finished.value = true
        return
      }
      pageNo.value++
      nextTick(() => onLoad())
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
    showToast({ message: err.msg })
    consecutiveEmptyPages.value++
    if (consecutiveEmptyPages.value >= maxEmptyPages) {
      finished.value = true
    }
  }
}

const onLoad = () => {
  if (!finished.value) {
    fetchGoodsList()
  }
}

onMounted(async () => {
  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }

  await userStore.queryDefaultAddr({ force: true })
  fetchGoodsList()
})
</script>

<style scoped lang="less">
.search-list-page {
  padding-top: 85px;

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 17px;
  }
}
</style>
