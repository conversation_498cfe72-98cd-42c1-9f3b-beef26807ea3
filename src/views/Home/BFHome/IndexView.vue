<template>
  <div class="bf-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <div class="banner-container">
      <BannerSkeleton v-if="skeletonStates.banner" />
      <GoodsImageSwiper v-else-if="headerBannerList.length > 0" :media-list="headerBannerList"
        mode="banner" paginationType="fraction" :autoplay="true" :loop="true"
        @image-click="handleBannerClick" />
    </div>

    <div class="grid-menu-container">
      <GridMenuSkeleton v-if="skeletonStates.gridMenu" />
      <GridMenu v-else-if="gridMenuItems.length > 0" :items="gridMenuItems" :columns="5"
        :show-more="true" :max-items="10" @item-click="handleGridItemClick" @more-click="handleMoreClick" />
    </div>

    <Block title="各县销冠" ref="limitedBlockRef">
      <van-list :loading="false" :finished="true" :immediate-check="false">
        <div class="waterfall-container">
          <!-- 添加过渡效果的容器 -->
          <transition name="waterfall-fade" mode="out-in">
            <WaterfallSkeleton v-if="skeletonStates.limited" :skeleton-count="6" key="skeleton" />
            <Waterfall v-else-if="limitedList.length > 0" ref="limitedWaterfallRef" key="waterfall"
              :list="limitedList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
              :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true">
              <template #default="{ item }">
                <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
              </template>
            </Waterfall>
          </transition>
        </div>

        <div
          class="load-more-container"
          v-if="limitedList.length > 0 && !limitedFinished && limitedButtonCanShow"
        >
          <WoButton
            type="text"
            :disabled="limitedLoading"
            @click="handleLimitedLoadMore"
            class="load-more-button"
          >
            {{ limitedLoading ? '加载中...' : '加载更多' }}
          </WoButton>
        </div>

        <div class="no-more-text" v-if="limitedList.length > 0 && limitedFinished">
          <span>没有更多了</span>
        </div>
      </van-list>
    </Block>

    <Block v-if="moduleShowStates.newer" title="新上好物" ref="newerBlockRef">
      <div class="horizontal-scroll-container">
        <HorizontalScrollSkeleton v-if="skeletonStates.newer" :skeleton-count="5" />
        <div v-else-if="newerList.length > 0" class="horizontal-scroll-wrapper">
          <div class="goods-item" v-for="item in newerList" :key="item.goodsId" @click="handleGoodsClick(item)">
            <GoodsCard :goods-info="item" @click="handleGoodsClick(item)" />
          </div>
        </div>
      </div>
    </Block>

    <Block v-if="moduleShowStates.hotProducts" title="爆款好物" ref="hotProductsBlockRef">
      <van-list :loading="false" :finished="true" :immediate-check="false">
        <div class="waterfall-container">
          <!-- 添加过渡效果的容器 -->
          <transition name="waterfall-fade" mode="out-in">
            <WaterfallSkeleton v-if="skeletonStates.hotProducts" :skeleton-count="6" key="skeleton" />
            <Waterfall v-else-if="hotProductsList.length > 0" ref="hotProductsWaterfallRef" key="waterfall"
              :list="hotProductsList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
              :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true">
              <template #default="{ item }">
                <GoodsCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
              </template>
            </Waterfall>
          </transition>
        </div>

        <div
          class="load-more-container"
          v-if="hotProductsList.length > 0 && !hotProductsFinished && hotProductsButtonCanShow"
        >
          <WoButton
            type="text"
            :disabled="hotProductsLoading"
            @click="handleHotProductsLoadMore"
            class="load-more-button"
          >
            {{ hotProductsLoading ? '加载中...' : '加载更多' }}
          </WoButton>
        </div>

        <div class="no-more-text" v-if="hotProductsList.length > 0 && hotProductsFinished">
          <span>没有更多了</span>
        </div>
      </van-list>
    </Block>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'
import GridMenu from '@components/Common/Home/GridMenu.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import Block from '@components/Common/Home/Block.vue'
import GoodsCard from '@components/Common/Home/GoodsCard.vue'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast } from 'vant'
import BannerSkeleton from '@components/Common/Home/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@components/Common/Home/Skeleton/GridMenuSkeleton.vue'
import WaterfallSkeleton from '@components/Common/Home/Skeleton/WaterfallSkeleton.vue'
import HorizontalScrollSkeleton from '@components/Common/Home/Skeleton/HorizontalScrollSkeleton.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

const router = useRouter()
const searchKeyword = ref('')
const headerBannerList = ref([])
const gridMenuItems = ref([])

const limitedList = ref([])
const limitedLoading = ref(false)
const limitedFinished = ref(false)
const limitedCurrentPage = ref(1)
const limitedPageSize = ref(10)
const limitedIsFirstLoadComplete = ref(false)
const limitedButtonCanShow = ref(false)

const newerList = ref([])
const newerLoading = ref(false)
const newerIsComplete = ref(false)

const hotProductsList = ref([])
const hotProductsLoading = ref(false)
const hotProductsFinished = ref(false)
const hotProductsCurrentPage = ref(1)
const hotProductsPageSize = ref(10)
const hotProductsIsFirstLoadComplete = ref(false)
const hotProductsButtonCanShow = ref(false)

const skeletonStates = ref({
  banner: true,
  gridMenu: true,
  limited: true,
  newer: true,
  hotProducts: true
})

const moduleShowStates = ref({
  limited: true,
  newer: false,
  hotProducts: false
})

const moduleDataReady = ref({
  banner: false,
  gridMenu: false,
  limited: false,
  newer: false,
  hotProducts: false
})

const breakpoints = ref({
  // 超大屏设备 (大平板横屏、桌面)
  1024: { rowPerView: 5 },

  // 大平板设备 (iPad Pro 12.9", iPad Air 横屏)
  834: { rowPerView: 4 },

  // 标准平板设备 (iPad 竖屏, 小平板横屏)
  768: { rowPerView: 4 },

  // 大屏手机横屏 & 小平板竖屏
  667: { rowPerView: 3 },

  // 大屏手机 (iPhone 14 Pro Max, iPhone 13 Pro Max 等)
  430: { rowPerView: 3 },

  // 标准大屏手机 (iPhone 14 Pro, iPhone 13 Pro, 大部分安卓旗舰)
  414: { rowPerView: 2 },

  // 中等屏幕手机 (iPhone 12/13 mini, 中端安卓)
  390: { rowPerView: 2 },

  // 标准屏幕手机 (iPhone SE 3rd, 大部分安卓中端机)
  375: { rowPerView: 2 },

  // 小屏手机 (老款 iPhone, 小屏安卓)
  360: { rowPerView: 2 },

  // 超小屏手机 (iPhone SE 1st/2nd, 老款安卓)
  320: { rowPerView: 2 },

  // 极小屏设备兜底 (折叠屏折叠状态等)
  0: { rowPerView: 1 }
})

const limitedWaterfallRef = ref(null)
const hotProductsWaterfallRef = ref(null)
const limitedBlockRef = ref(null)
const newerBlockRef = ref(null)
const hotProductsBlockRef = ref(null)

const intersectionObserver = ref(null)
const moduleInViewport = ref({
  limited: false,
  newer: false,
  hotProducts: false
})

const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}

const hideSkeletonInOrder = async () => {
  if (moduleDataReady.value.banner && skeletonStates.value.banner) {
    skeletonStates.value.banner = false
    await nextTick()
  }

  if (moduleDataReady.value.gridMenu && skeletonStates.value.gridMenu) {
    skeletonStates.value.gridMenu = false
    await nextTick()
  }

  if (moduleDataReady.value.limited && skeletonStates.value.limited) {
    // 延迟隐藏骨架屏，确保图片预加载完成
    await new Promise(resolve => setTimeout(resolve, 100))
    skeletonStates.value.limited = false
    await nextTick()
  }

  if (moduleDataReady.value.newer && skeletonStates.value.newer) {
    skeletonStates.value.newer = false
    await nextTick()
  }

  if (moduleDataReady.value.hotProducts && skeletonStates.value.hotProducts) {
    // 延迟隐藏骨架屏，确保图片预加载完成
    await new Promise(resolve => setTimeout(resolve, 100))
    skeletonStates.value.hotProducts = false
  }
}

const getHeaderBannerList = async () => {
  const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
  if (!err) {
    const bannerData = channelFilterd(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
    headerBannerList.value = bannerData
  }

  moduleDataReady.value.banner = true
  await hideSkeletonInOrder()
}

const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 2
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData.slice(0, 4)
    } else {
      gridMenuItems.value = []
    }
  }

  moduleDataReady.value.gridMenu = true
  await hideSkeletonInOrder()
}

const handleSearch = () => {
}

const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}

const handleGridItemClick = ({ item, index }) => {
  if (item.url) {
    window.location.href = item.url
  }
}

const handleMoreClick = () => {
}

const getLimitedList = async (isLoadMore = false) => {
  if (limitedLoading.value) return

  limitedLoading.value = true
  if (!isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: limitedCurrentPage.value,
    page_size: limitedPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  })

  if (!isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    await preloadImages(newItems.map(item => item.image))

    if (isLoadMore) {
      limitedList.value = [...limitedList.value, ...newItems]
      await nextTick()
      await waitForWaterfallRender(limitedWaterfallRef, 3000)
    } else {
      limitedList.value = newItems
      await nextTick()

      // 等待瀑布流渲染完成
      await waitForWaterfallRender(limitedWaterfallRef)

      // 额外等待一小段时间，确保布局稳定
      await new Promise(resolve => setTimeout(resolve, 150))

      if (!moduleDataReady.value.limited) {
        moduleDataReady.value.limited = true
        await hideSkeletonInOrder()
        await nextTick()

        limitedIsFirstLoadComplete.value = true
        limitedButtonCanShow.value = true
        await loadNextModule()

        if (moduleShowStates.value.newer && !moduleDataReady.value.newer) {
          await checkNewerModuleInViewport()
          if (moduleShowStates.value.newer && !moduleDataReady.value.newer) {
            await getNewerList()
          }
        }
      }
    }

    if (json.length === 0) {
      limitedFinished.value = true
    } else {
      limitedFinished.value = false
    }

    if (isLoadMore) {
      limitedCurrentPage.value++
    } else {
      limitedCurrentPage.value = 2
    }

  } else {
    limitedFinished.value = true
    if (!moduleDataReady.value.limited) {
      moduleDataReady.value.limited = true
      await hideSkeletonInOrder()
      await loadNextModule()
    }
  }

  limitedLoading.value = false
}
const handleLimitedLoadMore = () => {
  if (!limitedFinished.value && !limitedLoading.value && limitedIsFirstLoadComplete.value) {
    getLimitedList(true)
  }
}

const getNewerList = async () => {
  if (newerLoading.value) return

  newerLoading.value = true

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 10,
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    await preloadImages(newItems.map(item => item.image))
    newerList.value = newItems
    await nextTick()
  }

  if (!moduleDataReady.value.newer) {
    moduleDataReady.value.newer = true
    newerIsComplete.value = true
    await nextTick()
    await hideSkeletonInOrder()
    await loadNextModule()
  }

  newerLoading.value = false
}

const getHotProductsList = async (isLoadMore = false) => {
  if (hotProductsLoading.value) return

  hotProductsLoading.value = true

  if (isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: hotProductsCurrentPage.value,
    page_size: hotProductsPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    await preloadImages(newItems.map(item => item.image))

    if (isLoadMore) {
      hotProductsList.value = [...hotProductsList.value, ...newItems]
      await nextTick()
      await waitForWaterfallRender(hotProductsWaterfallRef, 3000)
    } else {
      hotProductsList.value = newItems
      await nextTick()

      // 等待瀑布流渲染完成
      await waitForWaterfallRender(hotProductsWaterfallRef)

      // 额外等待一小段时间，确保布局稳定
      await new Promise(resolve => setTimeout(resolve, 150))

      if (!moduleDataReady.value.hotProducts) {
        moduleDataReady.value.hotProducts = true
        await hideSkeletonInOrder()
        moduleShowStates.value.hotProducts = true
        hotProductsIsFirstLoadComplete.value = true
        hotProductsButtonCanShow.value = true
      }
    }

    if (json.length === 0) {
      hotProductsFinished.value = true
    } else {
      hotProductsFinished.value = false
    }

    if (isLoadMore) {
      hotProductsCurrentPage.value++
    } else {
      hotProductsCurrentPage.value = 2
    }
  } else {
    hotProductsFinished.value = true
    if (!moduleDataReady.value.hotProducts) {
      moduleDataReady.value.hotProducts = true
      await hideSkeletonInOrder()
      moduleShowStates.value.hotProducts = true
    }
  }

  hotProductsLoading.value = false
}

const handleHotProductsLoadMore = () => {
  if (!hotProductsFinished.value && !hotProductsLoading.value && hotProductsIsFirstLoadComplete.value) {
    getHotProductsList(true)
  }
}

const preloadImages = (imageUrls) => {
  return Promise.all(
    imageUrls.map(url => {
      return new Promise((resolve) => {
        if (!url) {
          resolve()
          return
        }
        const img = new Image()
        img.onload = () => {
          // 确保图片完全加载
          setTimeout(resolve, 50)
        }
        img.onerror = () => resolve()
        img.src = url

        // 设置超时，避免某些图片加载过慢
        setTimeout(resolve, 3000)
      })
    })
  )
}

const waitForWaterfallRender = async (waterfallRef, timeout = 5000) => {
  return new Promise((resolve) => {
    let checkCount = 0
    const maxChecks = timeout / 100

    const checkRender = () => {
      checkCount++

      if (!waterfallRef.value) {
        resolve()
        return
      }

      const waterfallEl = waterfallRef.value.$el || waterfallRef.value
      if (waterfallEl && waterfallEl.children && waterfallEl.children.length > 0) {
        const children = Array.from(waterfallEl.children)
        const allPositioned = children.every(child => {
          const style = window.getComputedStyle(child)
          return style.transform !== 'none' || (style.left !== 'auto' && style.top !== 'auto')
        })

        if (allPositioned) {
          resolve()
          return
        }
      }

      if (checkCount >= maxChecks) {
        resolve()
        return
      }

      Promise.resolve().then(checkRender)
    }

    Promise.resolve().then(checkRender)
  })
}

const handleGoodsClick = (goodsInfo) => {
  if (goodsInfo.goodsId) {
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}

const setupIntersectionObserver = () => {
  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const target = entry.target
        const isIntersecting = entry.isIntersecting

        if (target === limitedBlockRef.value?.$el) {
          moduleInViewport.value.limited = isIntersecting
        } else if (target === newerBlockRef.value?.$el) {
          moduleInViewport.value.newer = isIntersecting
          if (isIntersecting) {
            checkAndLoadNextModule('newer')
          }
        } else if (target === hotProductsBlockRef.value?.$el) {
          moduleInViewport.value.hotProducts = isIntersecting
          if (isIntersecting) {
            checkAndLoadNextModule('hotProducts')
          }
        }
      })
    },
    {
      rootMargin: '0px 0px 100px 0px',
      threshold: 0.1
    }
  )
}

const observeModules = () => {
  if (!intersectionObserver.value) {
    return
  }

  if (limitedBlockRef.value?.$el) {
    intersectionObserver.value.observe(limitedBlockRef.value.$el)
  }

  if (moduleShowStates.value.newer) {
    setupNewerModuleObserver()
  }

  if (moduleShowStates.value.hotProducts && hotProductsBlockRef.value?.$el) {
    intersectionObserver.value.observe(hotProductsBlockRef.value.$el)
  }
}

const canObserveNewerModule = () => {
  const canObserve = moduleDataReady.value.limited &&
                     limitedIsFirstLoadComplete.value &&
                     moduleShowStates.value.newer

  return canObserve
}

const setupNewerModuleObserver = () => {
  if (!intersectionObserver.value) {
    return
  }

  if (!newerBlockRef.value?.$el) {
    nextTick(() => {
      setupNewerModuleObserver()
    })
    return
  }

  if (moduleShowStates.value.newer) {
    intersectionObserver.value.observe(newerBlockRef.value.$el)
  }
}

const checkAndLoadNextModule = async (moduleName) => {
  if (moduleName === 'newer') {
    if (moduleShowStates.value.newer && !moduleDataReady.value.newer) {
      await getNewerList()
    }
  } else if (moduleName === 'hotProducts') {
    if (moduleDataReady.value.newer && !moduleDataReady.value.hotProducts && moduleInViewport.value.hotProducts) {
      await getHotProductsList(false)
    }
  }
}

const canLoadNewerModule = () => {
  const limitedComplete = moduleDataReady.value.limited && limitedIsFirstLoadComplete.value
  const newerModuleReady = moduleShowStates.value.newer

  return limitedComplete && newerModuleReady
}

const loadNextModule = async () => {
  if (moduleDataReady.value.limited && !moduleShowStates.value.newer) {
    moduleShowStates.value.newer = true
    await nextTick()

    await nextTick()
    setupNewerModuleObserver()
    await nextTick()
    await checkNewerModuleInViewport()
  }

  if (moduleDataReady.value.newer && !moduleShowStates.value.hotProducts) {
    moduleShowStates.value.hotProducts = true
    await nextTick()
    observeModules()
  }
}

// 计算预估的瀑布流高度，避免布局跳跃
const calculateEstimatedHeight = (itemCount, moduleType) => {
  if (itemCount === 0) return '200px'

  // 基于商品卡片的平均高度估算
  const avgCardHeight = 280 // GoodsCard 平均高度约 280px (图片180px + 内容100px)
  const columns = 2 // 瀑布流列数
  const gap = 12 // 间距
  const padding = 24 // 容器内边距

  const rows = Math.ceil(itemCount / columns)
  const estimatedHeight = rows * avgCardHeight + (rows - 1) * gap + padding

  return `${Math.max(estimatedHeight, 400)}px`
}

const getWaterfallMinHeight = (moduleType) => {
  if (moduleType === 'limited') {
    if (skeletonStates.value.limited) {
      return '680px'
    }
    // 使用预估高度而不是 auto，避免高度突变
    return calculateEstimatedHeight(limitedList.value.length, moduleType)
  } else if (moduleType === 'hotProducts') {
    if (skeletonStates.value.hotProducts) {
      return '680px'
    }
    return calculateEstimatedHeight(hotProductsList.value.length, moduleType)
  }
  return 'auto'
}

const checkNewerModuleInViewport = async () => {
  if (!newerBlockRef.value?.$el || moduleDataReady.value.newer) {
    return
  }

  const rect = newerBlockRef.value.$el.getBoundingClientRect()
  const isInViewport = rect.top < window.innerHeight + 100

  if (isInViewport) {
    moduleInViewport.value.newer = true
    await getNewerList()
  }
}


onMounted(() => {
  getHeaderBannerList()
  getIconList()
  setupIntersectionObserver()
  getLimitedList(false)

  nextTick(() => {
    observeModules()

    nextTick(() => {
      if (moduleDataReady.value.limited && !moduleShowStates.value.newer) {
        loadNextModule()
      }
    })
  })
})

onUnmounted(() => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }
})
</script>

<style scoped lang="less">
@import '@/assets/css/design-system.less';

.bf-home {
  width: 100vw;
  overflow: hidden;

  .banner-container {
    margin: @radius-8 @radius-12;
    border-radius: @radius-12;
    overflow: hidden;
  }

  .grid-menu-container {
    background: @bg-color-white;
    border-radius: @radius-12;
    margin: @radius-8 @radius-12;
  }

  .horizontal-scroll-container {
    position: relative;
    min-height: 180px;
    display: flex;
    align-items: center;

    .horizontal-scroll-wrapper {
      display: flex;
      gap: @radius-12;
      overflow-x: auto;
      padding-bottom: @radius-8;
      scroll-behavior: smooth;
      width: 100%;
      .no-scrollbar();

      .goods-item {
        flex: 0 0 160px;
        cursor: pointer;

        &:last-child {
          margin-right: @radius-12;
        }
      }
    }
  }

  .waterfall-container {
    position: relative;
    transition: min-height 0.3s ease;

    :deep(.vue-waterfall) {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  // 瀑布流过渡动画
  .waterfall-fade-enter-active,
  .waterfall-fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .waterfall-fade-enter-from,
  .waterfall-fade-leave-to {
    opacity: 0;
  }

  .waterfall-fade-enter-to,
  .waterfall-fade-leave-from {
    opacity: 1;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    color: @text-color-tertiary;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;

    .horizontal-scroll-container & {
      position: static;
      transform: none;
      padding: 60px 0;
    }
  }

  .home-block {
    &:not(:first-child) {
      margin-top: @radius-10;
    }

    .content {
      position: relative;
      overflow: hidden;
    }
  }

  .load-more-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .load-more-button {
      min-width: 120px;
      height: @button-height-36;
      font-size: @font-size-14;

      &.wo-button-text {
        background-color: transparent;
        border-radius: @radius-18;

        &:active {
          opacity: @opacity-07;
        }

        &.wo-button-disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:active {
            transform: none;
          }
        }
      }
    }
  }

  .no-more-text {
    padding: 20px 0 16px;
    text-align: center;

    span {
      font-size: @font-size-14;
      color: @text-color-tertiary;
    }
  }
}
</style>
